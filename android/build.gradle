allprojects {
    repositories {
        google()
        mavenCentral()
        maven {
            url 'https://artifact.bytedance.com/repository/pangle/'
        }
    }
    tasks.withType(JavaCompile).configureEach {
        javaCompiler = javaToolchains.compilerFor {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
